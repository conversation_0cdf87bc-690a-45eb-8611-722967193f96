<?php
/**
 * Orders Management Page
 *
 * This file handles the orders management in the admin panel
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Build query
$query = "
    SELECT o.*,
           u.full_name as customer_name,
           u.phone as customer_phone,
           dp.full_name as delivery_person_name
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    LEFT JOIN delivery_personnel dp ON o.delivery_personnel_id = dp.id
    WHERE 1=1
";

$countQuery = "SELECT COUNT(*) FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE 1=1";
$params = [];
$countParams = []; // Separate array for count query parameters

// Add search condition
if (!empty($search)) {
    $searchCondition = " AND (o.order_number LIKE ? OR o.tracking_number LIKE ? OR u.full_name LIKE ? OR u.phone LIKE ?)";
    $query .= $searchCondition;
    $countQuery .= $searchCondition;
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
    $countParams = array_merge($countParams, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

// Add status condition
if (!empty($status)) {
    $statusCondition = " AND o.status = ?";
    $query .= $statusCondition;
    $countQuery .= $statusCondition;
    $params[] = $status;
    $countParams[] = $status;
}

// Add date range condition
if (!empty($startDate) && !empty($endDate)) {
    $dateCondition = " AND DATE(o.created_at) BETWEEN ? AND ?";
    $query .= $dateCondition;
    $countQuery .= $dateCondition;
    $params[] = $startDate;
    $params[] = $endDate;
    $countParams[] = $startDate;
    $countParams[] = $endDate;
}

// Add order by
$query .= " ORDER BY o.created_at DESC";

// Add limit and offset
$query .= " LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;

// Execute count query
$stmt = $pdo->prepare($countQuery);
$stmt->execute($countParams);
$totalOrders = $stmt->fetchColumn();

// Calculate total pages
$totalPages = ceil($totalOrders / $limit);

// Execute main query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$orders = $stmt->fetchAll();

// Get delivery personnel for assignment
$stmt = $pdo->prepare("SELECT id, full_name, phone FROM delivery_personnel WHERE is_active = 1 ORDER BY full_name");
$stmt->execute();
$deliveryPersonnel = $stmt->fetchAll();

// Handle order status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $orderId = (int)$_POST['order_id'];
    $newStatus = $_POST['new_status'];
    $notes = $_POST['notes'] ?? '';
    $deliveryPersonnelId = !empty($_POST['delivery_personnel_id']) ? (int)$_POST['delivery_personnel_id'] : null;

    try {
        // Call stored procedure
        $stmt = $pdo->prepare("CALL update_order_status(?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $orderId,
            $newStatus,
            $notes,
            $adminData['id'],
            'admin',
            $deliveryPersonnelId
        ]);

        $_SESSION['success_message'] = 'Order status updated successfully.';
    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Failed to update order status: ' . $e->getMessage();
    }

    // Redirect to maintain pagination and search
    header("Location: orders.php?page=$page&search=" . urlencode($search) . "&status=" . urlencode($status) . "&start_date=" . urlencode($startDate) . "&end_date=" . urlencode($endDate));
    exit;
}

// Include header
$pageTitle = 'Orders Management';
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Orders Management</h1>

    <!-- Search and Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search and Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="orders.php" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Order #, Tracking #, Customer">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="status">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="placed" <?= $status === 'placed' ? 'selected' : '' ?>>Placed</option>
                        <option value="confirmed" <?= $status === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                        <option value="pickup_scheduled" <?= $status === 'pickup_scheduled' ? 'selected' : '' ?>>Pickup Scheduled</option>
                        <option value="picked_up" <?= $status === 'picked_up' ? 'selected' : '' ?>>Picked Up</option>
                        <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>Processing</option>
                        <option value="ready_for_delivery" <?= $status === 'ready_for_delivery' ? 'selected' : '' ?>>Ready for Delivery</option>
                        <option value="out_for_delivery" <?= $status === 'out_for_delivery' ? 'selected' : '' ?>>Out for Delivery</option>
                        <option value="delivered" <?= $status === 'delivered' ? 'selected' : '' ?>>Delivered</option>
                        <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="start_date">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="end_date">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">Search</button>
                    <a href="orders.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Orders</h6>
            <div>
                <a href="orders_export.php<?= !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : '' ?>" class="btn btn-sm btn-success">
                    <i class="fas fa-file-excel"></i> Export to Excel
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Tracking #</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Delivery Person</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($orders)): ?>
                            <tr>
                                <td colspan="8" class="text-center">No orders found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td><?= htmlspecialchars($order['order_number']) ?></td>
                                    <td><?= htmlspecialchars($order['tracking_number']) ?></td>
                                    <td>
                                        <?= htmlspecialchars($order['customer_name']) ?><br>
                                        <small><?= htmlspecialchars($order['customer_phone']) ?></small>
                                    </td>
                                    <td><?= date('M d, Y', strtotime($order['created_at'])) ?></td>
                                    <td><?= number_format($order['total'], 2) ?> BDT</td>
                                    <td>
                                        <span class="badge bg-<?= getStatusBadgeClass($order['status']) ?>">
                                            <?= formatStatus($order['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= $order['delivery_person_name'] ? htmlspecialchars($order['delivery_person_name']) : 'Not Assigned' ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary view-order" data-id="<?= $order['id'] ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-info update-status" data-id="<?= $order['id'] ?>" data-status="<?= $order['status'] ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mt-4">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="orders.php">
                <div class="modal-body">
                    <input type="hidden" name="order_id" id="update_order_id">
                    <input type="hidden" name="page" value="<?= $page ?>">
                    <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                    <input type="hidden" name="status" value="<?= htmlspecialchars($status) ?>">
                    <input type="hidden" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                    <input type="hidden" name="end_date" value="<?= htmlspecialchars($endDate) ?>">

                    <div class="form-group">
                        <label for="new_status">New Status</label>
                        <select class="form-control" id="new_status" name="new_status" required>
                            <option value="placed">Placed</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="pickup_scheduled">Pickup Scheduled</option>
                            <option value="picked_up">Picked Up</option>
                            <option value="processing">Processing</option>
                            <option value="ready_for_delivery">Ready for Delivery</option>
                            <option value="out_for_delivery">Out for Delivery</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="delivery_personnel_id">Assign Delivery Personnel</label>
                        <select class="form-control" id="delivery_personnel_id" name="delivery_personnel_id">
                            <option value="">Select Delivery Personnel</option>
                            <?php foreach ($deliveryPersonnel as $dp): ?>
                                <option value="<?= $dp['id'] ?>"><?= htmlspecialchars($dp['full_name']) ?> (<?= htmlspecialchars($dp['phone']) ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Order Modal -->
<div class="modal fade" id="viewOrderModal" tabindex="-1" role="dialog" aria-labelledby="viewOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewOrderModalLabel">Order Details</h5>
                <button type="button" class="btn-close" id="headerCloseBtn" data-bs-dismiss="modal" aria-label="Close" onclick="$('#viewOrderModal').modal('hide');"></button>
            </div>
            <div class="modal-body" id="orderDetails">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeOrderModal" data-bs-dismiss="modal" onclick="$('#viewOrderModal').modal('hide');">Close</button>
                <button type="button" class="btn btn-primary" id="printOrder">Print</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

<script>
    $(document).ready(function() {
        // Initialize Bootstrap 5 modals
        var modals = document.querySelectorAll('.modal');
        modals.forEach(function(modal) {
            new bootstrap.Modal(modal);
        });
        // Update Status Modal
        $('.update-status').click(function() {
            var orderId = $(this).data('id');
            var currentStatus = $(this).data('status');

            $('#update_order_id').val(orderId);
            $('#new_status').val(currentStatus);
            $('#updateStatusModal').modal('show');
        });

        // View Order Modal
        $('.view-order').click(function() {
            var orderId = $(this).data('id');
            console.log("Viewing order ID:", orderId);

            // Validate order ID
            if (!orderId || isNaN(parseInt(orderId))) {
                console.error("Invalid order ID:", orderId);
                $('#orderDetails').html('<div class="alert alert-danger">Invalid order ID. Please try again.</div>');
                $('#viewOrderModal').modal('show');
                return;
            }

            $('#orderDetails').html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
            $('#viewOrderModal').modal('show');

            // Debug information
            console.log("Making AJAX request to:", '../api/orders/details.php');
            console.log("With order_id:", orderId);

            // Load order details via AJAX
            $.ajax({
                url: '../api/orders/details.php',
                type: 'GET',
                data: {
                    order_id: orderId,
                    // Add timestamp to prevent caching issues
                    _t: new Date().getTime()
                },
                dataType: 'json',
                success: function(response) {
                    console.log("API Response:", response);
                    if (response.success) {
                        var order = response.data.order;
                        var items = response.data.items;
                        var statusHistory = response.data.status_history;

                        var html = '<div class="row">';

                        // Order Info
                        html += '<div class="col-md-6">';
                        html += '<h5>Order Information</h5>';
                        html += '<table class="table table-sm">';
                        html += '<tr><th>Order #:</th><td>' + order.order_number + '</td></tr>';
                        html += '<tr><th>Tracking #:</th><td>' + order.tracking_number + '</td></tr>';
                        html += '<tr><th>Date:</th><td>' + new Date(order.created_at).toLocaleString() + '</td></tr>';
                        html += '<tr><th>Status:</th><td><span class="badge bg-' + getStatusBadgeClass(order.status) + '">' + formatStatus(order.status) + '</span></td></tr>';
                        html += '<tr><th>Payment Method:</th><td>' + formatPaymentMethod(order.payment_method) + '</td></tr>';
                        html += '<tr><th>Payment Status:</th><td>' + formatPaymentStatus(order.payment_status) + '</td></tr>';
                        html += '</table>';
                        html += '</div>';

                        // Customer Info
                        html += '<div class="col-md-6">';
                        html += '<h5>Customer Information</h5>';
                        html += '<table class="table table-sm">';
                        html += '<tr><th>Name:</th><td>' + order.customer_name + '</td></tr>';
                        html += '<tr><th>Phone:</th><td>' + order.customer_phone + '</td></tr>';
                        html += '<tr><th>Pickup Address:</th><td>' + order.pickup_address + '</td></tr>';
                        html += '<tr><th>Pickup Date:</th><td>' + order.pickup_date + '</td></tr>';
                        html += '<tr><th>Pickup Time:</th><td>' + order.pickup_time_slot + '</td></tr>';
                        html += '<tr><th>Delivery Address:</th><td>' + order.delivery_address + '</td></tr>';
                        html += '</table>';
                        html += '</div>';

                        html += '</div>';

                        // Order Items
                        html += '<h5 class="mt-4">Order Items</h5>';
                        html += '<div class="table-responsive">';
                        html += '<table class="table table-bordered">';
                        html += '<thead><tr><th>Item</th><th>Service</th><th>Price</th><th>Quantity</th><th>Subtotal</th></tr></thead>';
                        html += '<tbody>';

                        var total = 0;
                        $.each(items, function(i, item) {
                            html += '<tr>';
                            html += '<td>' + item.name + '</td>';
                            html += '<td>' + item.service_name + '</td>';
                            html += '<td>' + parseFloat(item.price).toFixed(2) + ' BDT</td>';
                            html += '<td>' + item.quantity + '</td>';
                            html += '<td>' + parseFloat(item.subtotal).toFixed(2) + ' BDT</td>';
                            html += '</tr>';
                            total += parseFloat(item.subtotal);
                        });

                        html += '</tbody>';
                        html += '<tfoot>';
                        html += '<tr><th colspan="4" class="text-right">Subtotal:</th><td>' + parseFloat(order.subtotal).toFixed(2) + ' BDT</td></tr>';

                        if (parseFloat(order.discount) > 0) {
                            html += '<tr><th colspan="4" class="text-right">Discount:</th><td>-' + parseFloat(order.discount).toFixed(2) + ' BDT</td></tr>';
                        }

                        html += '<tr><th colspan="4" class="text-right">Delivery Fee:</th><td>' + parseFloat(order.delivery_fee).toFixed(2) + ' BDT</td></tr>';
                        html += '<tr><th colspan="4" class="text-right">Total:</th><td>' + parseFloat(order.total).toFixed(2) + ' BDT</td></tr>';
                        html += '</tfoot>';
                        html += '</table>';
                        html += '</div>';

                        // Status History
                        html += '<h5 class="mt-4">Status History</h5>';
                        html += '<div class="table-responsive">';
                        html += '<table class="table table-bordered">';
                        html += '<thead><tr><th>Status</th><th>Date</th><th>Updated By</th><th>Notes</th></tr></thead>';
                        html += '<tbody>';

                        $.each(statusHistory, function(i, history) {
                            html += '<tr>';
                            html += '<td><span class="badge bg-' + getStatusBadgeClass(history.status) + '">' + formatStatus(history.status) + '</span></td>';
                            html += '<td>' + new Date(history.created_at).toLocaleString() + '</td>';
                            html += '<td>' + (history.updated_by_name || 'System') + '</td>';
                            html += '<td>' + (history.notes || '-') + '</td>';
                            html += '</tr>';
                        });

                        html += '</tbody>';
                        html += '</table>';
                        html += '</div>';

                        $('#orderDetails').html(html);
                    } else {
                        $('#orderDetails').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    console.log("Response:", xhr.responseText);

                    // Try to parse the error response
                    let errorMessage = 'Failed to load order details. Please try again.';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error("Error parsing response:", e);
                    }

                    $('#orderDetails').html('<div class="alert alert-danger">' + errorMessage + '</div>');
                }
            });
        });

        // Ensure close buttons work properly
        $('#closeOrderModal, #headerCloseBtn').click(function() {
            $('#viewOrderModal').modal('hide');
        });

        // Also handle the data-bs-dismiss attribute for Bootstrap 5
        $('#viewOrderModal').on('hidden.bs.modal', function () {
            console.log('Modal hidden event triggered');
        });

        // Print Order - Using a better approach that doesn't break event handlers
        $('#printOrder').click(function() {
            // Create a new window for printing
            var printWindow = window.open('', '_blank', 'width=800,height=600');

            // Add necessary styles
            printWindow.document.write(`
                <html>
                <head>
                    <title>Order Details</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { padding: 20px; }
                        @media print {
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="row mb-4">
                            <div class="col-12">
                                <h3 class="text-center">GoGoLaundry - Order Details</h3>
                            </div>
                        </div>
                        <div class="content">
                            ${$('#orderDetails').html()}
                        </div>
                        <div class="row mt-4 no-print">
                            <div class="col-12 text-center">
                                <button type="button" class="btn btn-primary" onclick="window.print();">Print</button>
                                <button type="button" class="btn btn-secondary" onclick="window.close();">Close</button>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `);

            // Finish loading the document
            printWindow.document.close();

            // Focus the new window
            printWindow.focus();
        });
    });

    // Helper functions
    function getStatusBadgeClass(status) {
        switch (status) {
            case 'placed': return 'secondary';
            case 'confirmed': return 'info';
            case 'pickup_scheduled': return 'primary';
            case 'picked_up': return 'warning text-dark'; // Add text-dark for better visibility
            case 'processing': return 'warning text-dark'; // Add text-dark for better visibility
            case 'ready_for_delivery': return 'info';
            case 'out_for_delivery': return 'primary';
            case 'delivered': return 'success';
            case 'cancelled': return 'danger';
            default: return 'secondary';
        }
    }

    function formatStatus(status) {
        return status.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
    }

    function formatPaymentMethod(method) {
        switch (method) {
            case 'cash': return 'Cash on Delivery';
            case 'card': return 'Card Payment';
            case 'mobile_banking': return 'Mobile Banking';
            default: return method;
        }
    }

    function formatPaymentStatus(status) {
        switch (status) {
            case 'pending': return '<span class="badge bg-warning text-dark">Pending</span>';
            case 'paid': return '<span class="badge bg-success">Paid</span>';
            case 'failed': return '<span class="badge bg-danger">Failed</span>';
            case 'refunded': return '<span class="badge bg-info">Refunded</span>';
            default: return status;
        }
    }
</script>
<?php

/**
 * Get status badge class
 *
 * @param string $status Order status
 * @return string Badge class
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'placed': return 'secondary';
        case 'confirmed': return 'info';
        case 'pickup_scheduled': return 'primary';
        case 'picked_up': return 'warning text-dark'; // Add text-dark for better visibility
        case 'processing': return 'warning text-dark'; // Add text-dark for better visibility
        case 'ready_for_delivery': return 'info';
        case 'out_for_delivery': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Format status
 *
 * @param string $status Order status
 * @return string Formatted status
 */
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}
?>
