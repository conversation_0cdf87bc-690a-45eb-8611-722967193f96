<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/glass_section_background"
    tools:context=".ui.fragment.ShopMapFragment">

    <!-- Map View -->
    <org.osmdroid.views.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Modern Search Bar -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/searchCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:elevation="12dp"
        app:cardCornerRadius="28dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardElevation="0dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/search_bar_gradient_overlay"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:minHeight="56dp"
            android:clickable="true"
            android:focusable="true">

            <!-- Search Icon Container -->
            <FrameLayout
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="4dp">

                <ImageView
                    android:id="@+id/searchIcon"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_search_modern"
                    android:contentDescription="@string/search"
                    app:tint="@color/black" />

            </FrameLayout>

            <!-- Search Input -->
            <EditText
                android:id="@+id/searchEditText"
                style="@style/ModernSearchInputStyle"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="8dp"
                android:hint="@string/search_shops"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:drawablePadding="8dp" />

            <!-- Filter Button -->
            <FrameLayout
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp">

                <ImageButton
                    android:id="@+id/filterButton"
                    style="@style/ModernFilterButtonStyle"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_filter_modern"
                    android:contentDescription="@string/filter" />

            </FrameLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Current Location Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/currentLocationFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="80dp"
        android:src="@drawable/ic_my_location"
        android:contentDescription="@string/current_location"
        app:backgroundTint="@color/colorPrimary"
        app:tint="@color/white"
        app:elevation="8dp" />

    <!-- Shop List Toggle Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/shopListFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="144dp"
        android:src="@drawable/ic_list"
        android:contentDescription="@string/shop_list"
        app:backgroundTint="@color/colorSecondary"
        app:tint="@color/white"
        app:elevation="8dp" />

    <!-- Bottom Sheet for Shop List -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_gravity="bottom"
        android:background="@drawable/glass_section_background"
        android:visibility="gone"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Bottom Sheet Handle -->
            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bottom_sheet_handle"
                android:backgroundTint="@color/white_50" />

            <!-- Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/nearby_shops"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Shop List RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/shopsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_shop_card" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Loading Indicator -->
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/shimmer_shop_item" />
            <include layout="@layout/shimmer_shop_item" />
            <include layout="@layout/shimmer_shop_item" />

        </LinearLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
