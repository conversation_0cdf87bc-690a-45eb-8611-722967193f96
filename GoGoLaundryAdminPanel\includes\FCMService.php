<?php
/**
 * Firebase Cloud Messaging Service
 *
 * This class handles sending FCM notifications to both mobile and web clients
 */

// Check if vendor autoload exists and define availability
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
    define('FCM_DEPENDENCIES_AVAILABLE', true);
} else {
    define('FCM_DEPENDENCIES_AVAILABLE', false);
}

// Import classes only if dependencies are available
if (FCM_DEPENDENCIES_AVAILABLE) {
    // These will only be used if the classes are available
    // We'll use fully qualified class names in the code instead
}

// Simple JWT implementation for service account authentication
if (!FCM_DEPENDENCIES_AVAILABLE) {
    class SimpleJWT {
        public static function encode($payload, $key, $alg = 'RS256') {
            $header = json_encode(['typ' => 'JWT', 'alg' => $alg]);
            $payload = json_encode($payload);

            $headerEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
            $payloadEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

            $signature = '';
            $data = $headerEncoded . '.' . $payloadEncoded;

            if ($alg === 'RS256') {
                openssl_sign($data, $signature, $key, OPENSSL_ALGO_SHA256);
                $signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
            }

            return $data . '.' . $signature;
        }
    }
}

class FCMService {
    private $projectId;
    private $serviceAccountPath;
    private $accessToken;
    private $client;

    public function __construct() {
        $this->projectId = 'gogolaundry-c4dd1';
        $this->serviceAccountPath = __DIR__ . '/../../service_account.json';

        if (FCM_DEPENDENCIES_AVAILABLE) {
            $this->client = new \GuzzleHttp\Client();
        }

        // Get access token
        $this->accessToken = $this->getAccessToken();
    }

    /**
     * Get OAuth2 access token for FCM
     */
    private function getAccessToken() {
        if (!file_exists($this->serviceAccountPath)) {
            error_log('FCM: Service account file not found at: ' . $this->serviceAccountPath);
            return null;
        }

        try {
            $serviceAccount = json_decode(file_get_contents($this->serviceAccountPath), true);

            if (FCM_DEPENDENCIES_AVAILABLE) {
                // Use Google Auth library
                $credentials = new \Google\Auth\Credentials\ServiceAccountCredentials(
                    'https://www.googleapis.com/auth/firebase.messaging',
                    $serviceAccount
                );

                $token = $credentials->fetchAuthToken();
                return $token['access_token'];
            } else {
                // Use fallback implementation
                return $this->getAccessTokenFallback($serviceAccount);
            }
        } catch (Exception $e) {
            error_log('FCM: Failed to get access token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fallback method to get access token using cURL
     */
    private function getAccessTokenFallback($serviceAccount) {
        try {
            $now = time();
            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
                'aud' => 'https://oauth2.googleapis.com/token',
                'iat' => $now,
                'exp' => $now + 3600
            ];

            $jwt = SimpleJWT::encode($payload, $serviceAccount['private_key']);

            $postData = http_build_query([
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                return $data['access_token'] ?? null;
            } else {
                error_log('FCM: Failed to get access token. HTTP Code: ' . $httpCode . ', Response: ' . $response);
                return null;
            }
        } catch (Exception $e) {
            error_log('FCM: Fallback access token failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send notification to a single device token
     */
    public function sendToToken($token, $title, $message, $data = [], $deviceType = 'android') {
        if (!$this->accessToken) {
            return ['success' => false, 'error' => 'No access token available'];
        }

        $payload = $this->buildPayload($token, $title, $message, $data, $deviceType);

        if (FCM_DEPENDENCIES_AVAILABLE && $this->client) {
            // Use Guzzle HTTP client
            try {
                $response = $this->client->post(
                    "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send",
                    [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $this->accessToken,
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $payload
                    ]
                );

                $responseData = json_decode($response->getBody(), true);
                return ['success' => true, 'response' => $responseData];

            } catch (\GuzzleHttp\Exception\RequestException $e) {
                $errorMessage = $e->getMessage();
                if ($e->hasResponse()) {
                    $errorBody = $e->getResponse()->getBody()->getContents();
                    $errorMessage .= ' - ' . $errorBody;
                }

                error_log('FCM: Failed to send notification: ' . $errorMessage);
                return ['success' => false, 'error' => $errorMessage];
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();
                error_log('FCM: Failed to send notification: ' . $errorMessage);
                return ['success' => false, 'error' => $errorMessage];
            }
        } else {
            // Use cURL fallback
            return $this->sendWithCurl($payload);
        }
    }

    /**
     * Send notification using cURL
     */
    private function sendWithCurl($payload) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send");
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $responseData = json_decode($response, true);
                return ['success' => true, 'response' => $responseData];
            } else {
                error_log('FCM: Failed to send notification. HTTP Code: ' . $httpCode . ', Response: ' . $response);
                return ['success' => false, 'error' => 'HTTP ' . $httpCode . ': ' . $response];
            }
        } catch (Exception $e) {
            error_log('FCM: cURL send failed: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send notification to multiple device tokens
     */
    public function sendToMultipleTokens($tokens, $title, $message, $data = []) {
        $results = [];

        foreach ($tokens as $tokenData) {
            $token = is_array($tokenData) ? $tokenData['token'] : $tokenData;
            $deviceType = is_array($tokenData) ? $tokenData['device_type'] : 'android';

            $result = $this->sendToToken($token, $title, $message, $data, $deviceType);
            $results[] = [
                'token' => $token,
                'success' => $result['success'],
                'error' => $result['error'] ?? null
            ];
        }

        return $results;
    }

    /**
     * Send notification to all users
     */
    public function sendToAllUsers($pdo, $title, $message, $data = []) {
        try {
            // Get all active FCM tokens
            $stmt = $pdo->prepare("
                SELECT ft.token, ft.device_type, u.full_name
                FROM fcm_tokens ft
                JOIN users u ON ft.user_id = u.id
                WHERE ft.is_active = 1 AND u.is_verified = 1
            ");
            $stmt->execute();
            $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($tokens)) {
                return ['success' => false, 'error' => 'No active tokens found'];
            }

            return $this->sendToMultipleTokens($tokens, $title, $message, $data);

        } catch (Exception $e) {
            error_log('FCM: Failed to send to all users: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send notification to specific user
     */
    public function sendToUser($pdo, $userId, $title, $message, $data = []) {
        try {
            // Get user's active FCM tokens
            $stmt = $pdo->prepare("
                SELECT token, device_type
                FROM fcm_tokens
                WHERE user_id = ? AND is_active = 1
            ");
            $stmt->execute([$userId]);
            $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($tokens)) {
                return ['success' => false, 'error' => 'No active tokens found for user'];
            }

            return $this->sendToMultipleTokens($tokens, $title, $message, $data);

        } catch (Exception $e) {
            error_log('FCM: Failed to send to user: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Build FCM payload based on device type
     */
    private function buildPayload($token, $title, $message, $data, $deviceType) {
        // Ensure all data values are strings (FCM requirement)
        $stringData = [];
        foreach ($data as $key => $value) {
            $stringData[$key] = (string)$value;
        }

        $payload = [
            'message' => [
                'token' => $token,
                'data' => array_merge([
                    'title' => (string)$title,
                    'message' => (string)$message,
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ], $stringData)
            ]
        ];

        // For Android, use data-only payload to ensure proper handling
        // The Android app will create the notification from data payload
        if ($deviceType === 'android') {
            // Android: Data-only payload for better control
            // The FirebaseMessagingService will handle notification creation
        } else if ($deviceType === 'ios') {
            // iOS: Add notification payload for system handling
            $payload['message']['notification'] = [
                'title' => $title,
                'body' => $message
            ];
        } else if ($deviceType === 'web') {
            // Web specific configuration
            $payload['message']['webpush'] = [
                'notification' => [
                    'title' => $title,
                    'body' => $message,
                    'icon' => '/assets/images/logo.png',
                    'badge' => '/assets/images/badge.png',
                    'requireInteraction' => true,
                    'actions' => [
                        [
                            'action' => 'view',
                            'title' => 'View Details'
                        ]
                    ]
                ]
            ];
        }

        return $payload;
    }

    /**
     * Register or update FCM token
     */
    public function registerToken($pdo, $userId, $token, $deviceId, $deviceType = 'android') {
        try {
            // Check if token already exists for this user and device
            $stmt = $pdo->prepare("
                SELECT id FROM fcm_tokens
                WHERE user_id = ? AND device_id = ?
            ");
            $stmt->execute([$userId, $deviceId]);

            if ($stmt->rowCount() > 0) {
                // Update existing token
                $stmt = $pdo->prepare("
                    UPDATE fcm_tokens
                    SET token = ?, device_type = ?, is_active = 1, updated_at = NOW()
                    WHERE user_id = ? AND device_id = ?
                ");
                $stmt->execute([$token, $deviceType, $userId, $deviceId]);
            } else {
                // Insert new token
                $stmt = $pdo->prepare("
                    INSERT INTO fcm_tokens (user_id, device_id, token, device_type, is_active)
                    VALUES (?, ?, ?, ?, 1)
                ");
                $stmt->execute([$userId, $deviceId, $token, $deviceType]);
            }

            return ['success' => true, 'message' => 'Token registered successfully'];

        } catch (Exception $e) {
            error_log('FCM: Failed to register token: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Deactivate FCM token by token string
     */
    public function deactivateToken($pdo, $token) {
        try {
            $stmt = $pdo->prepare("
                UPDATE fcm_tokens
                SET is_active = 0
                WHERE token = ?
            ");
            $stmt->execute([$token]);

            return ['success' => true, 'message' => 'Token deactivated successfully'];

        } catch (Exception $e) {
            error_log('FCM: Failed to deactivate token: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Deactivate FCM token for specific user and device
     */
    public function deactivateUserDeviceToken($pdo, $userId, $deviceId) {
        try {
            $stmt = $pdo->prepare("
                UPDATE fcm_tokens
                SET is_active = 0
                WHERE user_id = ? AND device_id = ?
            ");
            $stmt->execute([$userId, $deviceId]);

            $count = $stmt->rowCount();

            return [
                'success' => true,
                'message' => 'User device token deactivated successfully',
                'count' => $count
            ];

        } catch (Exception $e) {
            error_log('FCM: Failed to deactivate user device token: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Deactivate all FCM tokens for a user
     */
    public function deactivateAllUserTokens($pdo, $userId) {
        try {
            $stmt = $pdo->prepare("
                UPDATE fcm_tokens
                SET is_active = 0
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);

            $count = $stmt->rowCount();

            return [
                'success' => true,
                'message' => 'All user tokens deactivated successfully',
                'count' => $count
            ];

        } catch (Exception $e) {
            error_log('FCM: Failed to deactivate all user tokens: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
