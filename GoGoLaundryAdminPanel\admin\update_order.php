<?php
/**
 * Update Order Page
 *
 * This page allows admins to update order status and assign delivery personnel
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/OrderManager.php';

// Initialize order manager
$orderManager = new OrderManager($pdo);

// Check if order ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'Invalid order ID provided.';
    header('Location: orders.php');
    exit;
}

$orderId = (int)$_GET['id'];

// Get order details
$order = $orderManager->getOrderById($orderId);

if (!$order) {
    $_SESSION['error_message'] = 'Order not found.';
    header('Location: orders.php');
    exit;
}

// Get delivery personnel for assignment
$stmt = $pdo->prepare("SELECT id, full_name, phone FROM delivery_personnel WHERE is_active = 1 ORDER BY full_name");
$stmt->execute();
$deliveryPersonnel = $stmt->fetchAll();

// Define order statuses
$orderStatuses = [
    'placed' => 'Placed',
    'confirmed' => 'Confirmed',
    'pickup_scheduled' => 'Pickup Scheduled',
    'picked_up' => 'Picked Up',
    'processing' => 'Processing',
    'ready_for_delivery' => 'Ready for Delivery',
    'out_for_delivery' => 'Out for Delivery',
    'delivered' => 'Delivered',
    'cancelled' => 'Cancelled'
];

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $newStatus = $_POST['status'] ?? '';
        $notes = trim($_POST['notes'] ?? '');
        $deliveryPersonnelId = !empty($_POST['delivery_personnel_id']) ? (int)$_POST['delivery_personnel_id'] : null;
        
        // Validate input
        if (empty($newStatus)) {
            $error = 'Please select a status.';
        } elseif (!array_key_exists($newStatus, $orderStatuses)) {
            $error = 'Invalid status selected.';
        } else {
            // Update order status
            $result = $orderManager->updateOrderStatus(
                $orderId,
                $newStatus,
                $notes,
                $adminData['id'],
                'admin',
                $deliveryPersonnelId
            );
            
            if ($result) {
                // Log action
                $adminManager->logAdminAction(
                    $adminData['id'],
                    'order_update',
                    'Updated order #' . $order['order_number'] . ' status to ' . $newStatus,
                    getClientIp()
                );
                
                $success = 'Order status updated successfully.';
                
                // Refresh order data
                $order = $orderManager->getOrderById($orderId);
            } else {
                $error = 'Failed to update order status. Please try again.';
            }
        }
    }
}

// Helper functions
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'placed': return 'secondary';
        case 'confirmed': return 'info';
        case 'pickup_scheduled': return 'primary';
        case 'picked_up': return 'warning text-dark';
        case 'processing': return 'warning text-dark';
        case 'ready_for_delivery': return 'info';
        case 'out_for_delivery': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}

// Page title and breadcrumbs
$pageTitle = 'Update Order #' . $order['order_number'];
$breadcrumbs = [
    'Orders' => 'orders.php',
    'Update Order' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Update Order #<?php echo htmlspecialchars($order['order_number']); ?></h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="order_details.php?id=<?php echo $orderId; ?>" class="btn btn-outline-info me-2">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
        <a href="orders.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i> Update Order Status
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Order Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <?php foreach ($orderStatuses as $statusValue => $statusLabel): ?>
                                <option value="<?php echo $statusValue; ?>" <?php echo ($order['status'] === $statusValue) ? 'selected' : ''; ?>>
                                    <?php echo $statusLabel; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="delivery_personnel_id" class="form-label">Delivery Personnel</label>
                        <select class="form-select" id="delivery_personnel_id" name="delivery_personnel_id">
                            <option value="">No Assignment</option>
                            <?php foreach ($deliveryPersonnel as $personnel): ?>
                                <option value="<?php echo $personnel['id']; ?>" <?php echo ($order['delivery_personnel_id'] == $personnel['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($personnel['full_name']); ?> - <?php echo htmlspecialchars($personnel['phone']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select a delivery person to assign this order to them.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Add any notes about this status update..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Order
                        </button>
                        <a href="orders.php" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i> Order Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Order Number</label>
                    <div class="fw-bold"><?php echo htmlspecialchars($order['order_number']); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Tracking Number</label>
                    <div class="text-muted"><?php echo htmlspecialchars($order['tracking_number']); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Current Status</label>
                    <div>
                        <span class="badge bg-<?php echo getStatusBadgeClass($order['status']); ?>">
                            <?php echo formatStatus($order['status']); ?>
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Customer</label>
                    <div><?php echo htmlspecialchars($order['customer_name']); ?></div>
                    <div class="text-muted small"><?php echo htmlspecialchars($order['customer_phone']); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Order Date</label>
                    <div class="text-muted"><?php echo date('M d, Y H:i', strtotime($order['created_at'])); ?></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Total Amount</label>
                    <div class="fw-bold text-success">৳<?php echo number_format($order['total'], 2); ?></div>
                </div>
                
                <?php if (!empty($order['delivery_person_name'])): ?>
                <div class="mb-0">
                    <label class="form-label">Current Delivery Personnel</label>
                    <div><?php echo htmlspecialchars($order['delivery_person_name']); ?></div>
                    <div class="text-muted small"><?php echo htmlspecialchars($order['delivery_person_phone']); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
