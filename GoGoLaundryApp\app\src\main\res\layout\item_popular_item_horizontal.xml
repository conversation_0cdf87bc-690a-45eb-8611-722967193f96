<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/card_stroke_light"
    app:strokeWidth="0.5dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Item Image Container -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/image_container"
            android:layout_width="0dp"
            android:layout_height="100dp"
            app:cardBackgroundColor="@color/background_light"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/item_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/item_image"
                android:scaleType="centerCrop"
                tools:src="@drawable/placeholder_image" />

            <!-- Out of Stock Overlay -->
            <FrameLayout
                android:id="@+id/out_of_stock_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#80000000"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/clean_section_background"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:text="@string/out_of_stock"
                    android:textColor="@color/text_primary"
                    android:textSize="10sp"
                    android:textStyle="bold" />
            </FrameLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Item Name -->
        <TextView
            android:id="@+id/item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_container"
            tools:text="Cotton T-Shirt" />

        <!-- Item Price -->
        <TextView
            android:id="@+id/item_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/add_to_cart_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_name"
            tools:text="৳25" />

        <!-- Add to Cart Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_to_cart_button"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            app:backgroundTint="@color/primary_light"
            app:cornerRadius="16dp"
            app:icon="@drawable/ic_add"
            app:iconGravity="textStart"
            app:iconSize="16dp"
            app:iconTint="@color/primary"
            app:layout_constraintBottom_toBottomOf="@id/item_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
