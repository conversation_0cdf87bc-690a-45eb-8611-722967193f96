<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_medium">

        <!-- Order Items Section -->
        <TextView
            android:id="@+id/items_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/order_items"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/checkout_items_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            app:layout_constraintTop_toBottomOf="@id/items_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/item_checkout"
            tools:itemCount="2" />

        <!-- Order Summary Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/summary_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card"
            app:layout_constraintTop_toBottomOf="@id/checkout_items_recycler_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/order_summary"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/margin_small" />

                <TextView
                    android:id="@+id/checkout_subtotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/subtotal"
                    android:textSize="@dimen/text_size_small"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/margin_extra_small" />

                <TextView
                    android:id="@+id/checkout_delivery_fee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery_fee"
                    android:textSize="@dimen/text_size_small"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/margin_extra_small" />

                <TextView
                    android:id="@+id/checkout_discount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/discount"
                    android:textSize="@dimen/text_size_small"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/margin_extra_small" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginTop="@dimen/margin_small"
                    android:layout_marginBottom="@dimen/margin_small" />

                <TextView
                    android:id="@+id/checkout_total"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/total"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Address Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/address_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card"
            app:layout_constraintTop_toBottomOf="@id/summary_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery_address"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/margin_small" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/address"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/address_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:minLines="2" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Address Picker Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/select_address_button"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="@dimen/margin_small"
                    android:text="Choose Address on Map"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    app:icon="@drawable/ic_map"
                    app:iconTint="@color/colorPrimary"
                    app:iconSize="18dp"
                    app:strokeColor="@color/colorPrimary"
                    app:strokeWidth="1dp"
                    app:cornerRadius="24dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/notes"
                    android:layout_marginTop="@dimen/margin_small"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/notes_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:minLines="2" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Pickup Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/pickup_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card"
            app:layout_constraintTop_toBottomOf="@id/address_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/pickup_details"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/margin_small" />

                <!-- Pickup Date Selection -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    android:background="@drawable/bg_date_time_picker_card"
                    android:stateListAnimator="@animator/date_time_card_state_animator"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_date_time_picker_ripple"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            android:layout_marginEnd="16dp"
                            app:cardBackgroundColor="@color/picker_selected_background">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_calendar_modern"
                                android:contentDescription="@string/date"
                                app:tint="@color/picker_gradient_end" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Pickup Date"
                                android:textSize="12sp"
                                android:textColor="@color/picker_unselected_text"
                                android:textStyle="normal"
                                android:letterSpacing="0.02" />

                            <TextView
                                android:id="@+id/pickup_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/select_date"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/picker_gradient_end"
                                android:layout_marginTop="2dp"
                                android:letterSpacing="0.01" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:contentDescription="Select"
                            app:tint="@color/picker_unselected_text"
                            android:alpha="0.6" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Pickup Time Selection -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    android:background="@drawable/bg_date_time_picker_card"
                    android:stateListAnimator="@animator/date_time_card_state_animator"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_date_time_picker_ripple"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            android:layout_marginEnd="16dp"
                            app:cardBackgroundColor="@color/picker_selected_background">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_time_modern"
                                android:contentDescription="@string/time"
                                app:tint="@color/picker_gradient_end" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Pickup Time"
                                android:textSize="12sp"
                                android:textColor="@color/picker_unselected_text"
                                android:textStyle="normal"
                                android:letterSpacing="0.02" />

                            <TextView
                                android:id="@+id/pickup_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/select_time"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/picker_gradient_end"
                                android:layout_marginTop="2dp"
                                android:letterSpacing="0.01" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:contentDescription="Select"
                            app:tint="@color/picker_unselected_text"
                            android:alpha="0.6" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Delivery Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/delivery_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card"
            app:layout_constraintTop_toBottomOf="@id/pickup_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery_details"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/margin_small" />

                <!-- Delivery Date Selection -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    android:background="@drawable/bg_date_time_picker_card"
                    android:stateListAnimator="@animator/date_time_card_state_animator"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_date_time_picker_ripple"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            android:layout_marginEnd="16dp"
                            app:cardBackgroundColor="@color/picker_selected_background">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_calendar_modern"
                                android:contentDescription="@string/date"
                                app:tint="@color/picker_gradient_end" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Delivery Date"
                                android:textSize="12sp"
                                android:textColor="@color/picker_unselected_text"
                                android:textStyle="normal"
                                android:letterSpacing="0.02" />

                            <TextView
                                android:id="@+id/delivery_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/select_date"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/picker_gradient_end"
                                android:layout_marginTop="2dp"
                                android:letterSpacing="0.01" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:contentDescription="Select"
                            app:tint="@color/picker_unselected_text"
                            android:alpha="0.6" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Delivery Time Selection -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    android:background="@drawable/bg_date_time_picker_card"
                    android:stateListAnimator="@animator/date_time_card_state_animator"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_date_time_picker_ripple"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            android:layout_marginEnd="16dp"
                            app:cardBackgroundColor="@color/picker_selected_background">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_time_modern"
                                android:contentDescription="@string/time"
                                app:tint="@color/picker_gradient_end" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Delivery Time"
                                android:textSize="12sp"
                                android:textColor="@color/picker_unselected_text"
                                android:textStyle="normal"
                                android:letterSpacing="0.02" />

                            <TextView
                                android:id="@+id/delivery_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/select_time"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/picker_gradient_end"
                                android:layout_marginTop="2dp"
                                android:letterSpacing="0.01" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:contentDescription="Select"
                            app:tint="@color/picker_unselected_text"
                            android:alpha="0.6" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Payment Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/payment_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card"
            app:layout_constraintTop_toBottomOf="@id/delivery_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/payment_method"
                    android:textSize="@dimen/text_size_medium"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/margin_small" />

                <Spinner
                    android:id="@+id/payment_method_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp" />

                <!-- Include payment details layout -->
                <include
                    android:id="@+id/payment_details"
                    layout="@layout/payment_details_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Place Order Button -->
        <Button
            android:id="@+id/place_order_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/place_order"
            android:layout_marginTop="@dimen/margin_large"
            android:layout_marginBottom="@dimen/margin_large"
            app:layout_constraintTop_toBottomOf="@id/payment_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
