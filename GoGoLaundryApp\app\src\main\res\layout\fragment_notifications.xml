<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

        <!-- Notifications Content Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <!-- Section Title -->
                <LinearLayout
                    android:id="@+id/notifications_content_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingBottom="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_notification"
                        app:tint="@color/primary"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Notifications"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Notifications List Container -->
                <FrameLayout
                    android:id="@+id/notifications_list_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="300dp"
                    app:layout_constraintTop_toBottomOf="@id/notifications_content_header">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/notifications_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:fadeScrollbars="true"
                        android:nestedScrollingEnabled="true"
                        android:paddingStart="8dp"
                        android:paddingTop="8dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="8dp"
                        android:scrollbarStyle="outsideOverlay"
                        android:scrollbars="vertical"
                        android:visibility="visible"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_notification" />

                    <!-- Loading Progress -->
                    <LinearLayout
                        android:id="@+id/progress_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="40dp"
                        android:visibility="gone">

                        <ProgressBar
                            android:id="@+id/progress_bar"
                            style="?android:attr/progressBarStyle"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:indeterminateTint="@color/primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="Loading notifications..."
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Empty State -->
                    <LinearLayout
                        android:id="@+id/empty_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="40dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:alpha="0.7"
                            android:src="@drawable/ic_notification"
                            app:tint="@color/primary"
                            tools:ignore="ContentDescription" />

                        <TextView
                            android:id="@+id/empty_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="No Notifications"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/empty_subtitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineSpacingExtra="2dp"
                            android:text="You're all caught up! No new notifications at the moment."
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Hidden Switch for Compatibility -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/show_read_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>