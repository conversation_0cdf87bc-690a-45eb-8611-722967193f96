<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Main glass background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1AFFFFFF" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Border for glass effect -->
    <item>
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#26FFFFFF" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="#4DFFFFFF" />
            <corners android:radius="15dp" />
        </shape>
    </item>
    
</layer-list>
