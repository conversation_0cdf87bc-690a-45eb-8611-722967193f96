<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".LoginActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="@dimen/elevation_appbar"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/login"
        android:fontFamily="@font/bnlatxf"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Password Login View -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/passwordLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium">

                <!-- Background Image -->
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:alpha="0.4"
                    android:scaleType="fitXY"
                    android:src="@drawable/approved"
                    tools:ignore="ContentDescription" />

                <!-- CardView Login Form (Centered) -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/draw"
                            android:layout_width="100dp"
                            android:layout_height="100dp"
                            android:layout_margin="5dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="25dp"
                            app:cardElevation="2dp"
                            app:cardMaxElevation="15dp">

                            <ImageView
                                android:id="@+id/splash_logo"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/profile"
                                tools:ignore="ContentDescription" />

                        </androidx.cardview.widget.CardView>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/phoneLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:hint="@string/phone_number"
                            app:boxStrokeColor="#2196F3"
                            app:hintTextColor="#2196F3"
                            app:startIconDrawable="@drawable/baseline_phone_24">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/phoneEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/bnlatxf"
                                android:inputType="phone" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="8dp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/passwordLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:hint="@string/password"
                            app:boxStrokeColor="#2196F3"
                            app:endIconMode="password_toggle"
                            app:hintTextColor="#2196F3"
                            app:startIconDrawable="@drawable/baseline_password_24">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/passwordEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/bnlatxf"
                                android:inputType="textPassword" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:id="@+id/forgotPasswordText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:gravity="end"
                            android:padding="4dp"
                            android:text="@string/forgot_password"
                            android:textColor="#2196F3"
                            android:textSize="14sp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/loginButton"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/button_height"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="20dp"
                            android:text="@string/login"
                            android:fontFamily="@font/bnlatxf"
                            app:cornerRadius="@dimen/button_corner_radius" />

                        <TextView
                            android:id="@+id/signupText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:gravity="center"
                            android:padding="16dp"
                            android:text="@string/dont_have_account"
                            android:textColor="#2196F3"
                            android:textSize="14sp" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Hidden OTP Login View (for compatibility) -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/otpLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium"
                android:visibility="gone" />
        </FrameLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
