<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".LoginActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="@dimen/elevation_appbar"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/login"
        android:fontFamily="@font/bnlatxf"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/passwordLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="24dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:strokeColor="@color/card_stroke_light"
                    app:strokeWidth="0.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp">

                        <ImageView
                            android:id="@+id/splash_logo"
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            android:layout_marginBottom="24dp"
                            android:background="@drawable/circle_background"
                            android:backgroundTint="@color/primary_light"
                            android:padding="16dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/profile"
                            app:tint="@color/primary"
                            tools:ignore="ContentDescription" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="Welcome Back"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="32dp"
                            android:text="Sign in to your account"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_secondary" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/phoneLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="@string/phone_number"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:startIconDrawable="@drawable/baseline_phone_24"
                            app:startIconTint="@color/primary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/phoneEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/passwordLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:hint="@string/password"
                            app:boxStrokeColor="@color/primary"
                            app:endIconMode="password_toggle"
                            app:endIconTint="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:startIconDrawable="@drawable/baseline_password_24"
                            app:startIconTint="@color/primary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/passwordEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPassword" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:id="@+id/forgotPasswordText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="24dp"
                            android:background="?attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="end"
                            android:minHeight="48dp"
                            android:padding="12dp"
                            android:text="@string/forgot_password"
                            android:textColor="@color/primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/loginButton"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:layout_marginBottom="16dp"
                            android:text="@string/login"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:cornerRadius="12dp" />

                        <TextView
                            android:id="@+id/signupText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="?attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:minHeight="48dp"
                            android:padding="16dp"
                            android:text="@string/dont_have_account"
                            android:textColor="@color/primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/otpLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium"
                android:visibility="gone" />
        </FrameLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>