package com.mdsadrulhasan.gogolaundry.database;

import androidx.room.Database;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.dao.ItemDao;
import com.mdsadrulhasan.gogolaundry.database.dao.LaundryShopDao;
import com.mdsadrulhasan.gogolaundry.database.dao.OrderDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ServiceDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopItemDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopServiceDao;
import com.mdsadrulhasan.gogolaundry.database.dao.UserDao;
import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.UserEntity;

/**
 * Main database class for the application
 */
@Database(
    entities = {
        UserEntity.class,
        ServiceEntity.class,
        OrderEntity.class,
        OrderItemEntity.class,
        ItemEntity.class,
        LaundryShopEntity.class,
        ShopServiceEntity.class,
        ShopItemEntity.class
    },
    version = 9, // Increased version number for new description and basePrice fields
    exportSchema = false
)
@TypeConverters({DateConverter.class})
public abstract class AppDatabase extends RoomDatabase {

    /**
     * Get user DAO
     *
     * @return User DAO
     */
    public abstract UserDao userDao();

    /**
     * Get service DAO
     *
     * @return Service DAO
     */
    public abstract ServiceDao serviceDao();

    /**
     * Get order DAO
     *
     * @return Order DAO
     */
    public abstract OrderDao orderDao();

    /**
     * Get item DAO
     *
     * @return Item DAO
     */
    public abstract ItemDao itemDao();

    /**
     * Get laundry shop DAO
     *
     * @return Laundry shop DAO
     */
    public abstract LaundryShopDao laundryShopDao();

    /**
     * Get shop service DAO
     *
     * @return Shop service DAO
     */
    public abstract ShopServiceDao shopServiceDao();

    /**
     * Get shop item DAO
     *
     * @return Shop item DAO
     */
    public abstract ShopItemDao shopItemDao();

}
